/**
 * 动作映射系统
 * 将检测到的人体动作映射到虚拟环境中的交互
 */
import type { World } from '../../core/World';
import type { Entity } from '../../core/Entity';
import { EventEmitter } from '../../utils/EventEmitter';
import { Debug } from '../../utils/Debug';
import { Vector3, Quaternion } from 'three';
import { PoseResults, HandResults } from '../mediapipe/MediaPipePoseDetector';
import { LandmarkData } from '../types/LandmarkData';
import { GrabberComponent } from '../../interaction/components/GrabberComponent';
import { Hand } from '../../interaction/components/GrabbableComponent';

/**
 * 手势类型
 */
export enum GestureType {
  OPEN_HAND = 'open_hand',
  CLOSED_FIST = 'closed_fist',
  POINTING = 'pointing',
  PEACE_SIGN = 'peace_sign',
  THUMBS_UP = 'thumbs_up',
  GRAB = 'grab',
  RELEASE = 'release'
}

/**
 * 手势识别结果
 */
export interface GestureResult {
  type: GestureType;
  confidence: number;
  hand: 'left' | 'right';
  position: Vector3;
  timestamp: number;
}

/**
 * 交互动作类型
 */
export enum InteractionAction {
  GRAB_OBJECT = 'grab_object',
  RELEASE_OBJECT = 'release_object',
  MOVE_OBJECT = 'move_object',
  POINT_AT_OBJECT = 'point_at_object',
  WAVE_HAND = 'wave_hand',
  GESTURE_COMMAND = 'gesture_command'
}

/**
 * 交互事件
 */
export interface InteractionEvent {
  action: InteractionAction;
  entity: Entity;
  targetObject?: Entity;
  position: Vector3;
  gesture?: GestureResult;
  confidence: number;
  timestamp: number;
}

/**
 * 动作映射配置
 */
export interface ActionMappingConfig {
  /** 是否启用手势识别 */
  enableGestureRecognition: boolean;
  /** 是否启用物体交互 */
  enableObjectInteraction: boolean;
  /** 抓取检测阈值 */
  grabThreshold: number;
  /** 释放检测阈值 */
  releaseThreshold: number;
  /** 交互距离阈值 */
  interactionDistance: number;
  /** 手势置信度阈值 */
  gestureConfidenceThreshold: number;
  /** 是否启用调试模式 */
  debug: boolean;
}

/**
 * 动作映射系统
 */
export class ActionMappingSystem extends EventEmitter {
  private world: World;
  private config: ActionMappingConfig;
  private lastGestures: Map<string, GestureResult> = new Map();
  private lastHandPositions: Map<string, Vector3> = new Map();
  private grabbedObjects: Map<Entity, Entity> = new Map(); // entity -> grabbed object

  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: ActionMappingConfig = {
    enableGestureRecognition: true,
    enableObjectInteraction: true,
    grabThreshold: 0.7,
    releaseThreshold: 0.3,
    interactionDistance: 2.0,
    gestureConfidenceThreshold: 0.6,
    debug: false
  };

  constructor(world: World, config: Partial<ActionMappingConfig> = {}) {
    super();
    this.world = world;
    this.config = { ...ActionMappingSystem.DEFAULT_CONFIG, ...config };
  }

  /**
   * 处理手部交互
   */
  public processHandInteraction(entity: Entity, handResults: HandResults): void {
    if (!this.config.enableObjectInteraction) {
      return;
    }

    try {
      // 处理左手
      if (handResults.leftHand) {
        this.processHandGesture(entity, handResults.leftHand, 'left');
      }

      // 处理右手
      if (handResults.rightHand) {
        this.processHandGesture(entity, handResults.rightHand, 'right');
      }

    } catch (error) {
      Debug.error('ActionMappingSystem', '处理手部交互失败', error);
    }
  }

  /**
   * 处理单手手势
   */
  private processHandGesture(entity: Entity, handLandmarks: LandmarkData[], handType: 'left' | 'right'): void {
    // 计算手部位置
    const handPosition = this.calculateHandPosition(handLandmarks);
    const handKey = `${entity.id}_${handType}`;
    
    // 识别手势
    const gesture = this.recognizeGesture(handLandmarks, handType, handPosition);
    
    if (gesture && gesture.confidence > this.config.gestureConfidenceThreshold) {
      const lastGesture = this.lastGestures.get(handKey);
      
      // 检测手势变化
      if (!lastGesture || lastGesture.type !== gesture.type) {
        this.handleGestureChange(entity, gesture, lastGesture);
      }
      
      // 更新手势状态
      this.lastGestures.set(handKey, gesture);
    }
    
    // 更新手部位置
    this.lastHandPositions.set(handKey, handPosition);
    
    // 处理持续的交互动作
    this.processContinuousInteraction(entity, handPosition, handType);
  }

  /**
   * 计算手部位置
   */
  private calculateHandPosition(handLandmarks: LandmarkData[]): Vector3 {
    // 使用手腕位置作为手部中心
    const wrist = handLandmarks[0]; // MediaPipe手部关键点0是手腕
    
    if (wrist) {
      return new Vector3(
        (wrist.x - 0.5) * 2, // 转换到[-1, 1]范围
        -(wrist.y - 0.5) * 2, // Y轴翻转
        wrist.z || 0
      );
    }
    
    return new Vector3();
  }

  /**
   * 识别手势
   */
  private recognizeGesture(handLandmarks: LandmarkData[], handType: 'left' | 'right', position: Vector3): GestureResult | null {
    if (!this.config.enableGestureRecognition || handLandmarks.length < 21) {
      return null;
    }

    try {
      // 计算手指弯曲度
      const fingerCurvatures = this.calculateFingerCurvatures(handLandmarks);
      
      // 抓取手势检测
      if (this.isGrabGesture(fingerCurvatures)) {
        return {
          type: GestureType.GRAB,
          confidence: this.calculateGrabConfidence(fingerCurvatures),
          hand: handType,
          position,
          timestamp: Date.now()
        };
      }
      
      // 张开手势检测
      if (this.isOpenHandGesture(fingerCurvatures)) {
        return {
          type: GestureType.OPEN_HAND,
          confidence: this.calculateOpenHandConfidence(fingerCurvatures),
          hand: handType,
          position,
          timestamp: Date.now()
        };
      }
      
      // 指向手势检测
      if (this.isPointingGesture(fingerCurvatures)) {
        return {
          type: GestureType.POINTING,
          confidence: this.calculatePointingConfidence(fingerCurvatures),
          hand: handType,
          position,
          timestamp: Date.now()
        };
      }
      
      // 竖拇指手势检测
      if (this.isThumbsUpGesture(fingerCurvatures)) {
        return {
          type: GestureType.THUMBS_UP,
          confidence: this.calculateThumbsUpConfidence(fingerCurvatures),
          hand: handType,
          position,
          timestamp: Date.now()
        };
      }

    } catch (error) {
      Debug.error('ActionMappingSystem', '手势识别失败', error);
    }

    return null;
  }

  /**
   * 计算手指弯曲度
   */
  private calculateFingerCurvatures(handLandmarks: LandmarkData[]): {
    thumb: number;
    index: number;
    middle: number;
    ring: number;
    pinky: number;
  } {
    // MediaPipe手部关键点索引
    const fingerTips = [4, 8, 12, 16, 20]; // 拇指、食指、中指、无名指、小指指尖
    const fingerPips = [3, 6, 10, 14, 18]; // 对应的PIP关节
    const fingerMcps = [2, 5, 9, 13, 17]; // 对应的MCP关节

    const curvatures = {
      thumb: 0,
      index: 0,
      middle: 0,
      ring: 0,
      pinky: 0
    };

    const fingerNames = ['thumb', 'index', 'middle', 'ring', 'pinky'] as const;

    for (let i = 0; i < 5; i++) {
      const tip = handLandmarks[fingerTips[i]];
      const pip = handLandmarks[fingerPips[i]];
      const mcp = handLandmarks[fingerMcps[i]];

      if (tip && pip && mcp) {
        // 计算弯曲度：指尖到MCP的距离 vs PIP到MCP的距离
        const tipToMcp = Math.sqrt(
          Math.pow(tip.x - mcp.x, 2) + 
          Math.pow(tip.y - mcp.y, 2)
        );
        const pipToMcp = Math.sqrt(
          Math.pow(pip.x - mcp.x, 2) + 
          Math.pow(pip.y - mcp.y, 2)
        );

        // 弯曲度 = 1 - (实际距离 / 最大可能距离)
        curvatures[fingerNames[i]] = Math.max(0, 1 - (tipToMcp / (pipToMcp * 2)));
      }
    }

    return curvatures;
  }

  /**
   * 检测抓取手势
   */
  private isGrabGesture(curvatures: any): boolean {
    // 除拇指外的四个手指都弯曲
    return curvatures.index > this.config.grabThreshold &&
           curvatures.middle > this.config.grabThreshold &&
           curvatures.ring > this.config.grabThreshold &&
           curvatures.pinky > this.config.grabThreshold;
  }

  /**
   * 检测张开手势
   */
  private isOpenHandGesture(curvatures: any): boolean {
    // 所有手指都伸直
    return curvatures.thumb < this.config.releaseThreshold &&
           curvatures.index < this.config.releaseThreshold &&
           curvatures.middle < this.config.releaseThreshold &&
           curvatures.ring < this.config.releaseThreshold &&
           curvatures.pinky < this.config.releaseThreshold;
  }

  /**
   * 检测指向手势
   */
  private isPointingGesture(curvatures: any): boolean {
    // 食指伸直，其他手指弯曲
    return curvatures.index < this.config.releaseThreshold &&
           curvatures.middle > this.config.grabThreshold &&
           curvatures.ring > this.config.grabThreshold &&
           curvatures.pinky > this.config.grabThreshold;
  }

  /**
   * 检测竖拇指手势
   */
  private isThumbsUpGesture(curvatures: any): boolean {
    // 拇指伸直，其他手指弯曲
    return curvatures.thumb < this.config.releaseThreshold &&
           curvatures.index > this.config.grabThreshold &&
           curvatures.middle > this.config.grabThreshold &&
           curvatures.ring > this.config.grabThreshold &&
           curvatures.pinky > this.config.grabThreshold;
  }

  /**
   * 计算抓取置信度
   */
  private calculateGrabConfidence(curvatures: any): number {
    const avgCurvature = (curvatures.index + curvatures.middle + curvatures.ring + curvatures.pinky) / 4;
    return Math.min(1, avgCurvature);
  }

  /**
   * 计算张开手置信度
   */
  private calculateOpenHandConfidence(curvatures: any): number {
    const avgStraightness = 1 - (curvatures.thumb + curvatures.index + curvatures.middle + curvatures.ring + curvatures.pinky) / 5;
    return Math.max(0, avgStraightness);
  }

  /**
   * 计算指向置信度
   */
  private calculatePointingConfidence(curvatures: any): number {
    const indexStraightness = 1 - curvatures.index;
    const othersCurvature = (curvatures.middle + curvatures.ring + curvatures.pinky) / 3;
    return (indexStraightness + othersCurvature) / 2;
  }

  /**
   * 计算竖拇指置信度
   */
  private calculateThumbsUpConfidence(curvatures: any): number {
    const thumbStraightness = 1 - curvatures.thumb;
    const othersCurvature = (curvatures.index + curvatures.middle + curvatures.ring + curvatures.pinky) / 4;
    return (thumbStraightness + othersCurvature) / 2;
  }

  /**
   * 处理手势变化
   */
  private handleGestureChange(entity: Entity, newGesture: GestureResult, lastGesture?: GestureResult): void {
    const interactionEvent: InteractionEvent = {
      action: this.gestureToAction(newGesture.type),
      entity,
      position: newGesture.position,
      gesture: newGesture,
      confidence: newGesture.confidence,
      timestamp: newGesture.timestamp
    };

    // 处理抓取动作
    if (newGesture.type === GestureType.GRAB && (!lastGesture || lastGesture.type !== GestureType.GRAB)) {
      this.handleGrabAction(entity, newGesture);
    }
    
    // 处理释放动作
    if (newGesture.type === GestureType.OPEN_HAND && lastGesture?.type === GestureType.GRAB) {
      this.handleReleaseAction(entity, newGesture);
    }

    this.emit('interactionEvent', interactionEvent);

    if (this.config.debug) {
      Debug.log('ActionMappingSystem', `手势变化: ${lastGesture?.type || 'none'} -> ${newGesture.type}`, {
        entity: entity.id,
        hand: newGesture.hand,
        confidence: newGesture.confidence
      });
    }
  }

  /**
   * 处理抓取动作
   */
  private handleGrabAction(entity: Entity, gesture: GestureResult): void {
    const grabberComponent = entity.getComponent('GrabberComponent') as GrabberComponent;
    if (!grabberComponent) {
      return;
    }

    // 查找附近的可抓取物体
    const nearbyObjects = this.findNearbyGrabbableObjects(gesture.position);
    
    if (nearbyObjects.length > 0) {
      const targetObject = nearbyObjects[0];
      const hand = gesture.hand === 'left' ? Hand.LEFT : Hand.RIGHT;
      
      const success = grabberComponent.grab(targetObject, hand);
      
      if (success) {
        this.grabbedObjects.set(entity, targetObject);
        
        this.emit('objectGrabbed', {
          entity,
          targetObject,
          hand: gesture.hand,
          position: gesture.position
        });

        if (this.config.debug) {
          Debug.log('ActionMappingSystem', `实体 ${entity.id} 抓取了物体 ${targetObject.id}`);
        }
      }
    }
  }

  /**
   * 处理释放动作
   */
  private handleReleaseAction(entity: Entity, gesture: GestureResult): void {
    const grabberComponent = entity.getComponent('GrabberComponent') as GrabberComponent;
    if (!grabberComponent) {
      return;
    }

    const hand = gesture.hand === 'left' ? Hand.LEFT : Hand.RIGHT;
    const releasedObject = grabberComponent.release(hand);
    
    if (releasedObject) {
      this.grabbedObjects.delete(entity);
      
      this.emit('objectReleased', {
        entity,
        targetObject: releasedObject,
        hand: gesture.hand,
        position: gesture.position
      });

      if (this.config.debug) {
        Debug.log('ActionMappingSystem', `实体 ${entity.id} 释放了物体 ${releasedObject.id}`);
      }
    }
  }

  /**
   * 处理持续交互
   */
  private processContinuousInteraction(entity: Entity, handPosition: Vector3, handType: 'left' | 'right'): void {
    const grabbedObject = this.grabbedObjects.get(entity);
    
    if (grabbedObject) {
      // 移动被抓取的物体
      this.moveGrabbedObject(entity, grabbedObject, handPosition, handType);
    }
  }

  /**
   * 移动被抓取的物体
   */
  private moveGrabbedObject(entity: Entity, grabbedObject: Entity, handPosition: Vector3, handType: 'left' | 'right'): void {
    const transform = grabbedObject.getComponent('Transform');
    if (transform) {
      // 将手部位置转换为世界坐标
      const worldPosition = this.handPositionToWorldPosition(entity, handPosition);

      // 更新物体位置
      (transform as any).setPosition(worldPosition);

      this.emit('objectMoved', {
        entity,
        targetObject: grabbedObject,
        hand: handType,
        position: worldPosition
      });
    }
  }

  /**
   * 将手部位置转换为世界坐标
   */
  private handPositionToWorldPosition(entity: Entity, handPosition: Vector3): Vector3 {
    const entityTransform = entity.getComponent('Transform');
    if (entityTransform) {
      // 简单的相对位置转换，实际应用中可能需要更复杂的变换
      const entityPosition = (entityTransform as any).getPosition();
      return entityPosition.add(handPosition);
    }

    return handPosition.clone();
  }

  /**
   * 查找附近的可抓取物体
   */
  private findNearbyGrabbableObjects(position: Vector3): Entity[] {
    const nearbyObjects: Entity[] = [];
    
    // 遍历世界中的所有实体
    for (const entity of this.world.getEntities().values()) {
      const grabbableComponent = entity.getComponent('GrabbableComponent');
      const transform = entity.getComponent('Transform');

      if (grabbableComponent && transform) {
        const transformPosition = (transform as any).getPosition();
        const distance = position.distanceTo(transformPosition);

        if (distance <= this.config.interactionDistance) {
          nearbyObjects.push(entity);
        }
      }
    }
    
    // 按距离排序
    nearbyObjects.sort((a, b) => {
      const transformA = a.getComponent('Transform');
      const transformB = b.getComponent('Transform');

      if (!transformA || !transformB) return 0;

      const positionA = (transformA as any).getPosition();
      const positionB = (transformB as any).getPosition();
      const distanceA = position.distanceTo(positionA);
      const distanceB = position.distanceTo(positionB);

      return distanceA - distanceB;
    });
    
    return nearbyObjects;
  }

  /**
   * 将手势类型转换为交互动作
   */
  private gestureToAction(gestureType: GestureType): InteractionAction {
    switch (gestureType) {
      case GestureType.GRAB:
        return InteractionAction.GRAB_OBJECT;
      case GestureType.OPEN_HAND:
        return InteractionAction.RELEASE_OBJECT;
      case GestureType.POINTING:
        return InteractionAction.POINT_AT_OBJECT;
      case GestureType.THUMBS_UP:
        return InteractionAction.GESTURE_COMMAND;
      default:
        return InteractionAction.WAVE_HAND;
    }
  }

  /**
   * 处理姿态交互
   */
  public processPoseInteraction(entity: Entity, poseResults: PoseResults): void {
    // 这里可以添加基于全身姿态的交互逻辑
    // 例如：身体倾斜控制移动方向、蹲下触发特殊动作等
    
    if (this.config.debug && poseResults.landmarks) {
      // 简单的姿态分析示例
      const shoulderLeft = poseResults.landmarks[11];
      const shoulderRight = poseResults.landmarks[12];
      
      if (shoulderLeft && shoulderRight) {
        const shoulderTilt = shoulderLeft.y - shoulderRight.y;
        
        if (Math.abs(shoulderTilt) > 0.1) {
          this.emit('postureTilt', {
            entity,
            tilt: shoulderTilt,
            direction: shoulderTilt > 0 ? 'left' : 'right'
          });
        }
      }
    }
  }

  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<ActionMappingConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.emit('configUpdated', this.config);
  }

  /**
   * 获取配置
   */
  public getConfig(): ActionMappingConfig {
    return { ...this.config };
  }

  /**
   * 清理资源
   */
  public destroy(): void {
    this.lastGestures.clear();
    this.lastHandPositions.clear();
    this.grabbedObjects.clear();
    this.removeAllListeners();
  }
}
